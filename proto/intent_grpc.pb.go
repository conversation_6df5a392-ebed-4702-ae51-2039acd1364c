// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.5.1
// - protoc             v3.21.12
// source: proto/intent.proto

package intent

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.64.0 or later.
const _ = grpc.SupportPackageIsVersion9

const (
	IntentService_DetectIntent_FullMethodName = "/intent.IntentService/DetectIntent"
)

// IntentServiceClient is the client API for IntentService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type IntentServiceClient interface {
	DetectIntent(ctx context.Context, in *IntentRequest, opts ...grpc.CallOption) (*IntentResponse, error)
}

type intentServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewIntentServiceClient(cc grpc.ClientConnInterface) IntentServiceClient {
	return &intentServiceClient{cc}
}

func (c *intentServiceClient) DetectIntent(ctx context.Context, in *IntentRequest, opts ...grpc.CallOption) (*IntentResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(IntentResponse)
	err := c.cc.Invoke(ctx, IntentService_DetectIntent_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// IntentServiceServer is the server API for IntentService service.
// All implementations must embed UnimplementedIntentServiceServer
// for forward compatibility.
type IntentServiceServer interface {
	DetectIntent(context.Context, *IntentRequest) (*IntentResponse, error)
	mustEmbedUnimplementedIntentServiceServer()
}

// UnimplementedIntentServiceServer must be embedded to have
// forward compatible implementations.
//
// NOTE: this should be embedded by value instead of pointer to avoid a nil
// pointer dereference when methods are called.
type UnimplementedIntentServiceServer struct{}

func (UnimplementedIntentServiceServer) DetectIntent(context.Context, *IntentRequest) (*IntentResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DetectIntent not implemented")
}
func (UnimplementedIntentServiceServer) mustEmbedUnimplementedIntentServiceServer() {}
func (UnimplementedIntentServiceServer) testEmbeddedByValue()                       {}

// UnsafeIntentServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to IntentServiceServer will
// result in compilation errors.
type UnsafeIntentServiceServer interface {
	mustEmbedUnimplementedIntentServiceServer()
}

func RegisterIntentServiceServer(s grpc.ServiceRegistrar, srv IntentServiceServer) {
	// If the following call pancis, it indicates UnimplementedIntentServiceServer was
	// embedded by pointer and is nil.  This will cause panics if an
	// unimplemented method is ever invoked, so we test this at initialization
	// time to prevent it from happening at runtime later due to I/O.
	if t, ok := srv.(interface{ testEmbeddedByValue() }); ok {
		t.testEmbeddedByValue()
	}
	s.RegisterService(&IntentService_ServiceDesc, srv)
}

func _IntentService_DetectIntent_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(IntentRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(IntentServiceServer).DetectIntent(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: IntentService_DetectIntent_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(IntentServiceServer).DetectIntent(ctx, req.(*IntentRequest))
	}
	return interceptor(ctx, in, info, handler)
}

// IntentService_ServiceDesc is the grpc.ServiceDesc for IntentService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var IntentService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "intent.IntentService",
	HandlerType: (*IntentServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "DetectIntent",
			Handler:    _IntentService_DetectIntent_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "proto/intent.proto",
}
