// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.9
// 	protoc        v3.21.12
// source: proto/intent.proto

package intent

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type MessagePart struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Type          string                 `protobuf:"bytes,1,opt,name=type,proto3" json:"type,omitempty"`
	Text          string                 `protobuf:"bytes,2,opt,name=text,proto3" json:"text,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *MessagePart) Reset() {
	*x = MessagePart{}
	mi := &file_proto_intent_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *MessagePart) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MessagePart) ProtoMessage() {}

func (x *MessagePart) ProtoReflect() protoreflect.Message {
	mi := &file_proto_intent_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MessagePart.ProtoReflect.Descriptor instead.
func (*MessagePart) Descriptor() ([]byte, []int) {
	return file_proto_intent_proto_rawDescGZIP(), []int{0}
}

func (x *MessagePart) GetType() string {
	if x != nil {
		return x.Type
	}
	return ""
}

func (x *MessagePart) GetText() string {
	if x != nil {
		return x.Text
	}
	return ""
}

type Message struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Role          string                 `protobuf:"bytes,1,opt,name=role,proto3" json:"role,omitempty"`
	Content       string                 `protobuf:"bytes,2,opt,name=content,proto3" json:"content,omitempty"`
	Parts         []*MessagePart         `protobuf:"bytes,3,rep,name=parts,proto3" json:"parts,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *Message) Reset() {
	*x = Message{}
	mi := &file_proto_intent_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Message) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Message) ProtoMessage() {}

func (x *Message) ProtoReflect() protoreflect.Message {
	mi := &file_proto_intent_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Message.ProtoReflect.Descriptor instead.
func (*Message) Descriptor() ([]byte, []int) {
	return file_proto_intent_proto_rawDescGZIP(), []int{1}
}

func (x *Message) GetRole() string {
	if x != nil {
		return x.Role
	}
	return ""
}

func (x *Message) GetContent() string {
	if x != nil {
		return x.Content
	}
	return ""
}

func (x *Message) GetParts() []*MessagePart {
	if x != nil {
		return x.Parts
	}
	return nil
}

type IntentRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Id            string                 `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	Messages      []*Message             `protobuf:"bytes,2,rep,name=messages,proto3" json:"messages,omitempty"`
	UserId        string                 `protobuf:"bytes,3,opt,name=userId,proto3" json:"userId,omitempty"`
	SessionId     string                 `protobuf:"bytes,4,opt,name=sessionId,proto3" json:"sessionId,omitempty"`
	UserEmail     string                 `protobuf:"bytes,5,opt,name=userEmail,proto3" json:"userEmail,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *IntentRequest) Reset() {
	*x = IntentRequest{}
	mi := &file_proto_intent_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *IntentRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*IntentRequest) ProtoMessage() {}

func (x *IntentRequest) ProtoReflect() protoreflect.Message {
	mi := &file_proto_intent_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use IntentRequest.ProtoReflect.Descriptor instead.
func (*IntentRequest) Descriptor() ([]byte, []int) {
	return file_proto_intent_proto_rawDescGZIP(), []int{2}
}

func (x *IntentRequest) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *IntentRequest) GetMessages() []*Message {
	if x != nil {
		return x.Messages
	}
	return nil
}

func (x *IntentRequest) GetUserId() string {
	if x != nil {
		return x.UserId
	}
	return ""
}

func (x *IntentRequest) GetSessionId() string {
	if x != nil {
		return x.SessionId
	}
	return ""
}

func (x *IntentRequest) GetUserEmail() string {
	if x != nil {
		return x.UserEmail
	}
	return ""
}

type IntentMeta struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Route         string                 `protobuf:"bytes,1,opt,name=route,proto3" json:"route,omitempty"`
	Aggregation   string                 `protobuf:"bytes,2,opt,name=aggregation,proto3" json:"aggregation,omitempty"`
	Granularity   string                 `protobuf:"bytes,3,opt,name=granularity,proto3" json:"granularity,omitempty"`
	Dimensions    []string               `protobuf:"bytes,4,rep,name=dimensions,proto3" json:"dimensions,omitempty"`
	Metrics       []string               `protobuf:"bytes,5,rep,name=metrics,proto3" json:"metrics,omitempty"`
	Entities      []string               `protobuf:"bytes,6,rep,name=entities,proto3" json:"entities,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *IntentMeta) Reset() {
	*x = IntentMeta{}
	mi := &file_proto_intent_proto_msgTypes[3]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *IntentMeta) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*IntentMeta) ProtoMessage() {}

func (x *IntentMeta) ProtoReflect() protoreflect.Message {
	mi := &file_proto_intent_proto_msgTypes[3]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use IntentMeta.ProtoReflect.Descriptor instead.
func (*IntentMeta) Descriptor() ([]byte, []int) {
	return file_proto_intent_proto_rawDescGZIP(), []int{3}
}

func (x *IntentMeta) GetRoute() string {
	if x != nil {
		return x.Route
	}
	return ""
}

func (x *IntentMeta) GetAggregation() string {
	if x != nil {
		return x.Aggregation
	}
	return ""
}

func (x *IntentMeta) GetGranularity() string {
	if x != nil {
		return x.Granularity
	}
	return ""
}

func (x *IntentMeta) GetDimensions() []string {
	if x != nil {
		return x.Dimensions
	}
	return nil
}

func (x *IntentMeta) GetMetrics() []string {
	if x != nil {
		return x.Metrics
	}
	return nil
}

func (x *IntentMeta) GetEntities() []string {
	if x != nil {
		return x.Entities
	}
	return nil
}

type Intent struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Label         string                 `protobuf:"bytes,1,opt,name=label,proto3" json:"label,omitempty"`
	Confidence    float64                `protobuf:"fixed64,2,opt,name=confidence,proto3" json:"confidence,omitempty"`
	Meta          *IntentMeta            `protobuf:"bytes,3,opt,name=meta,proto3" json:"meta,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *Intent) Reset() {
	*x = Intent{}
	mi := &file_proto_intent_proto_msgTypes[4]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Intent) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Intent) ProtoMessage() {}

func (x *Intent) ProtoReflect() protoreflect.Message {
	mi := &file_proto_intent_proto_msgTypes[4]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Intent.ProtoReflect.Descriptor instead.
func (*Intent) Descriptor() ([]byte, []int) {
	return file_proto_intent_proto_rawDescGZIP(), []int{4}
}

func (x *Intent) GetLabel() string {
	if x != nil {
		return x.Label
	}
	return ""
}

func (x *Intent) GetConfidence() float64 {
	if x != nil {
		return x.Confidence
	}
	return 0
}

func (x *Intent) GetMeta() *IntentMeta {
	if x != nil {
		return x.Meta
	}
	return nil
}

type IntentResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Intent        *Intent                `protobuf:"bytes,1,opt,name=intent,proto3" json:"intent,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *IntentResponse) Reset() {
	*x = IntentResponse{}
	mi := &file_proto_intent_proto_msgTypes[5]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *IntentResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*IntentResponse) ProtoMessage() {}

func (x *IntentResponse) ProtoReflect() protoreflect.Message {
	mi := &file_proto_intent_proto_msgTypes[5]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use IntentResponse.ProtoReflect.Descriptor instead.
func (*IntentResponse) Descriptor() ([]byte, []int) {
	return file_proto_intent_proto_rawDescGZIP(), []int{5}
}

func (x *IntentResponse) GetIntent() *Intent {
	if x != nil {
		return x.Intent
	}
	return nil
}

var File_proto_intent_proto protoreflect.FileDescriptor

const file_proto_intent_proto_rawDesc = "" +
	"\n" +
	"\x12proto/intent.proto\x12\x06intent\"5\n" +
	"\vMessagePart\x12\x12\n" +
	"\x04type\x18\x01 \x01(\tR\x04type\x12\x12\n" +
	"\x04text\x18\x02 \x01(\tR\x04text\"b\n" +
	"\aMessage\x12\x12\n" +
	"\x04role\x18\x01 \x01(\tR\x04role\x12\x18\n" +
	"\acontent\x18\x02 \x01(\tR\acontent\x12)\n" +
	"\x05parts\x18\x03 \x03(\v2\x13.intent.MessagePartR\x05parts\"\xa0\x01\n" +
	"\rIntentRequest\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\tR\x02id\x12+\n" +
	"\bmessages\x18\x02 \x03(\v2\x0f.intent.MessageR\bmessages\x12\x16\n" +
	"\x06userId\x18\x03 \x01(\tR\x06userId\x12\x1c\n" +
	"\tsessionId\x18\x04 \x01(\tR\tsessionId\x12\x1c\n" +
	"\tuserEmail\x18\x05 \x01(\tR\tuserEmail\"\xbc\x01\n" +
	"\n" +
	"IntentMeta\x12\x14\n" +
	"\x05route\x18\x01 \x01(\tR\x05route\x12 \n" +
	"\vaggregation\x18\x02 \x01(\tR\vaggregation\x12 \n" +
	"\vgranularity\x18\x03 \x01(\tR\vgranularity\x12\x1e\n" +
	"\n" +
	"dimensions\x18\x04 \x03(\tR\n" +
	"dimensions\x12\x18\n" +
	"\ametrics\x18\x05 \x03(\tR\ametrics\x12\x1a\n" +
	"\bentities\x18\x06 \x03(\tR\bentities\"f\n" +
	"\x06Intent\x12\x14\n" +
	"\x05label\x18\x01 \x01(\tR\x05label\x12\x1e\n" +
	"\n" +
	"confidence\x18\x02 \x01(\x01R\n" +
	"confidence\x12&\n" +
	"\x04meta\x18\x03 \x01(\v2\x12.intent.IntentMetaR\x04meta\"8\n" +
	"\x0eIntentResponse\x12&\n" +
	"\x06intent\x18\x01 \x01(\v2\x0e.intent.IntentR\x06intent2P\n" +
	"\rIntentService\x12?\n" +
	"\fDetectIntent\x12\x15.intent.IntentRequest\x1a\x16.intent.IntentResponse\"\x00B\x17Z\x15./proto/intent;intentb\x06proto3"

var (
	file_proto_intent_proto_rawDescOnce sync.Once
	file_proto_intent_proto_rawDescData []byte
)

func file_proto_intent_proto_rawDescGZIP() []byte {
	file_proto_intent_proto_rawDescOnce.Do(func() {
		file_proto_intent_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_proto_intent_proto_rawDesc), len(file_proto_intent_proto_rawDesc)))
	})
	return file_proto_intent_proto_rawDescData
}

var file_proto_intent_proto_msgTypes = make([]protoimpl.MessageInfo, 6)
var file_proto_intent_proto_goTypes = []any{
	(*MessagePart)(nil),    // 0: intent.MessagePart
	(*Message)(nil),        // 1: intent.Message
	(*IntentRequest)(nil),  // 2: intent.IntentRequest
	(*IntentMeta)(nil),     // 3: intent.IntentMeta
	(*Intent)(nil),         // 4: intent.Intent
	(*IntentResponse)(nil), // 5: intent.IntentResponse
}
var file_proto_intent_proto_depIdxs = []int32{
	0, // 0: intent.Message.parts:type_name -> intent.MessagePart
	1, // 1: intent.IntentRequest.messages:type_name -> intent.Message
	3, // 2: intent.Intent.meta:type_name -> intent.IntentMeta
	4, // 3: intent.IntentResponse.intent:type_name -> intent.Intent
	2, // 4: intent.IntentService.DetectIntent:input_type -> intent.IntentRequest
	5, // 5: intent.IntentService.DetectIntent:output_type -> intent.IntentResponse
	5, // [5:6] is the sub-list for method output_type
	4, // [4:5] is the sub-list for method input_type
	4, // [4:4] is the sub-list for extension type_name
	4, // [4:4] is the sub-list for extension extendee
	0, // [0:4] is the sub-list for field type_name
}

func init() { file_proto_intent_proto_init() }
func file_proto_intent_proto_init() {
	if File_proto_intent_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_proto_intent_proto_rawDesc), len(file_proto_intent_proto_rawDesc)),
			NumEnums:      0,
			NumMessages:   6,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_proto_intent_proto_goTypes,
		DependencyIndexes: file_proto_intent_proto_depIdxs,
		MessageInfos:      file_proto_intent_proto_msgTypes,
	}.Build()
	File_proto_intent_proto = out.File
	file_proto_intent_proto_goTypes = nil
	file_proto_intent_proto_depIdxs = nil
}
